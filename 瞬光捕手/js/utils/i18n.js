/**
 * 瞬光捕手 - 国际化支持模块
 * 提供中英文切换功能
 */

class I18nService {
    constructor() {
        this.currentLanguage = 'zh-CN';
        this.translations = {};
        this.initialized = false;

        // 立即加载翻译数据（同步操作）
        this.loadTranslations();

        // 异步初始化其他部分
        this.init().catch(error => {
            console.error('i18n 服务初始化失败:', error);
        });
    }

    /**
     * 初始化国际化服务
     */
    async init() {
        // 等待存储服务初始化完成
        if (window.storageService && !window.storageService.initialized) {
            await this.waitForStorageService();
        }

        // 从存储中恢复语言设置
        let savedLanguage = 'zh-CN';
        try {
            if (window.storageService && window.storageService.initialized) {
                savedLanguage = await storageService.get('settings.language', 'zh-CN');
            }
        } catch (error) {
            console.warn('无法从存储中恢复语言设置，使用默认语言:', error);
        }

        this.currentLanguage = savedLanguage;

        // 应用翻译
        this.applyTranslations();
        this.initialized = true;

        console.log(`国际化服务初始化完成，当前语言: ${this.currentLanguage}`);
    }

    /**
     * 等待存储服务初始化完成
     */
    async waitForStorageService() {
        return new Promise((resolve) => {
            const checkStorage = () => {
                if (window.storageService && window.storageService.initialized) {
                    resolve();
                } else {
                    setTimeout(checkStorage, 50);
                }
            };
            checkStorage();
        });
    }

    /**
     * 加载翻译数据
     */
    loadTranslations() {
        this.translations = {
            'zh-CN': {
                // 游戏基本信息
                'game.title': '瞬光捕手',
                'game.subtitle': '捕捉决定性瞬间，引燃无限可能',
                'loading.text': '正在加载...',
                
                // 主菜单
                'menu.start': '开始游戏',
                'menu.levelEditor': '关卡编辑器',
                'menu.customLevels': '自定义关卡',
                'menu.leaderboard': '排行榜',
                'menu.settings': '设置',
                
                // 游戏界面
                'game.score': '得分',
                'game.level': '关卡',
                'game.lives': '生命',
                'game.pause': '暂停',
                'game.hint.ready': '准备好了吗？',
                'game.hint.start': '点击开始！',
                'game.hint.perfect': '完美时机！',
                'game.hint.good': '不错！',
                'game.hint.miss': '错过了！',
                'game.hint.gameOver': '游戏结束',
                
                // 暂停菜单
                'pause.title': '游戏暂停',
                'pause.resume': '继续游戏',
                'pause.restart': '重新开始',
                'pause.mainMenu': '主菜单',
                
                // 游戏结束
                'gameOver.title': '游戏结束',
                'gameOver.finalScore': '最终得分',
                'gameOver.newRecord': '新纪录！',
                'gameOver.playAgain': '再玩一次',
                'gameOver.backToMenu': '返回主菜单',
                
                // 设置
                'settings.title': '设置',
                'settings.language': '语言',
                'settings.sound': '音效',
                'settings.music': '音乐',
                'settings.save': '保存设置',
                'settings.cancel': '取消',
                
                // 玩家管理
                'player.current': '当前玩家',
                'player.switch': '切换',
                'player.management': '玩家管理',
                'player.existing': '现有玩家',
                'player.createNew': '创建新玩家',
                'player.namePlaceholder': '输入玩家名称',
                'player.create': '创建',
                'player.select': '选择',
                'player.delete': '删除',
                'player.guest': '游客',
                
                // 排行榜
                'leaderboard.title': '排行榜',
                'leaderboard.global': '全球排行',
                'leaderboard.daily': '今日排行',
                'leaderboard.weekly': '本周排行',
                'leaderboard.monthly': '本月排行',
                'leaderboard.perfectHits': '完美击中',
                'leaderboard.combo': '最高连击',
                'leaderboard.rank': '排名',
                'leaderboard.player': '玩家',
                'leaderboard.score': '分数',
                'leaderboard.level': '关卡',
                'leaderboard.time': '时间',
                'leaderboard.date': '日期',
                'leaderboard.refresh': '刷新',
                'leaderboard.loading': '加载中...',
                'leaderboard.empty': '暂无排行榜数据',
                'leaderboard.beFirst': '成为第一个上榜的玩家吧！',
                'leaderboard.error': '加载排行榜失败',
                'leaderboard.yourRank': '您的排名',
                'leaderboard.outOf': '/',
                'leaderboard.yourScore': '您的分数',
                'leaderboard.notRanked': '您还未上榜',
                
                // 关卡编辑器
                'editor.title': '关卡编辑器',
                'editor.tools': '工具',
                'editor.select': '选择',
                'editor.spark': '光点',
                'editor.obstacle': '障碍',
                'editor.powerup': '道具',
                'editor.trigger': '触发',
                'editor.eraser': '橡皮',
                'editor.actions': '操作',
                'editor.new': '新建',
                'editor.load': '加载',
                'editor.save': '保存',
                'editor.test': '测试',
                'editor.publish': '发布',
                'editor.view': '视图',
                'editor.showGrid': '显示网格',
                'editor.snapToGrid': '对齐网格',
                'editor.levelSettings': '关卡设置',
                'editor.levelName': '关卡名称',
                'editor.levelDescription': '关卡描述',
                'editor.difficulty': '难度',
                'editor.difficulty.easy': '简单',
                'editor.difficulty.normal': '普通',
                'editor.difficulty.hard': '困难',
                'editor.difficulty.expert': '专家',
                'editor.timeLimit': '时间限制(秒)',
                'editor.targetScore': '目标分数',
                'editor.objectProperties': '对象属性',
                'editor.selectTool': '请选择工具',
                'editor.statistics': '统计信息',
                'editor.totalObjects': '总对象数',
                'editor.sparkCount': '光点数',
                'editor.obstacleCount': '障碍数',
                'editor.powerupCount': '道具数',
                'editor.newLevel': '新关卡',
                'editor.noObjectsToTest': '关卡中没有对象可以测试',
                'editor.testFailed': '测试关卡失败',
                'editor.validationFailed': '关卡验证失败',
                'editor.publishSuccess': '关卡发布成功！',
                'editor.publishFailed': '关卡发布失败',
                'editor.noLevel': '没有关卡',
                'editor.nameRequired': '关卡名称不能为空',
                'editor.noObjects': '关卡中没有对象',
                'editor.noSparks': '关卡中没有光点',
                'editor.objectsOutOfBounds': '有对象超出边界',
                'editor.selectSubType': '请选择子类型',
                'editor.loadLevel': '加载关卡',
                'editor.loadSelected': '加载选中',
                'editor.noLevels': '暂无保存的关卡',
                'editor.noDescription': '无描述',
                'editor.selectLevel': '请选择一个关卡',
                'editor.loadFailed': '加载关卡失败',
                'editor.saveSuccess': '关卡保存成功！',
                'editor.saveFailed': '关卡保存失败',
                'editor.sparkProperties': '光点属性',
                'editor.obstacleProperties': '障碍物属性',
                'editor.powerupProperties': '道具属性',
                'editor.spark.normal': '普通光点',
                'editor.spark.fast': '快速光点',
                'editor.spark.slow': '缓慢光点',
                'editor.spark.bonus': '奖励光点',
                'editor.spark.perfect': '完美光点',
                'editor.obstacle.wall': '墙壁',
                'editor.obstacle.moving_wall': '移动墙壁',
                'editor.obstacle.spike': '尖刺',
                'editor.powerup.slow_time': '时间减缓',
                'editor.powerup.double_score': '双倍分数',
                'editor.powerup.shield': '护盾',
                'editor.powerup.multi_hit': '多重击中',
                
                // 自定义关卡
                'customLevels.title': '自定义关卡',
                'customLevels.subtitle': '探索玩家创造的精彩关卡',
                'customLevels.myLevels': '我的关卡',
                'customLevels.allLevels': '所有关卡',
                'customLevels.play': '游玩',
                'customLevels.edit': '编辑',
                'customLevels.delete': '删除',
                'customLevels.like': '点赞',
                'customLevels.dislike': '踩',
                'customLevels.liked': '已点赞',
                'customLevels.disliked': '已踩',
                'customLevels.rating': '评分',
                'customLevels.plays': '游玩次数',
                'customLevels.playCount': '游玩次数',
                'customLevels.author': '作者',
                'customLevels.difficulty': '难度',
                'customLevels.created': '创建时间',
                'customLevels.description': '关卡描述',
                'customLevels.preview': '关卡预览',
                'customLevels.details': '详情',
                'customLevels.myLevel': '我的',
                'customLevels.empty': '暂无自定义关卡',
                'customLevels.empty.title': '暂无关卡',
                'customLevels.empty.message': '还没有找到符合条件的关卡',
                'customLevels.createFirst': '创建第一个关卡',
                'customLevels.loading': '加载关卡中...',
                'customLevels.totalLevels': '总关卡数',
                'customLevels.noDescription': '暂无描述',
                'customLevels.emptyLevel': '空关卡',
                'customLevels.filter': '过滤',
                'customLevels.filter.published': '已发布',
                'customLevels.filter.my': '我的关卡',
                'customLevels.filter.all': '全部',
                'customLevels.sort': '排序',
                'customLevels.sort.rating': '评分',
                'customLevels.sort.playCount': '游玩次数',
                'customLevels.sort.date': '创建时间',
                'customLevels.sort.difficulty': '难度',
                'customLevels.search': '搜索关卡...',
                'customLevels.searchBtn': '搜索',
                'customLevels.loginToRate': '登录后评分',
                'customLevels.noPermission': '没有权限',
                'customLevels.confirmDelete': '确定要删除关卡 "{{name}}" 吗？',
                'customLevels.deleteSuccess': '关卡删除成功',
                'customLevels.deleteFailed': '关卡删除失败',
                'customLevels.loadFailed': '关卡加载失败',
                'customLevels.playFailed': '游玩关卡失败',
                'customLevels.rateFailed': '评分失败',
                
                // 通用
                'common.ok': '确定',
                'common.cancel': '取消',
                'common.close': '关闭',
                'common.back': '返回',
                'common.next': '下一步',
                'common.previous': '上一步',
                'common.loading': '加载中...',
                'common.error': '错误',
                'common.success': '成功',
                'common.warning': '警告',
                'common.confirm': '确认',

                // 时间格式
                'time.justNow': '刚刚',
                'time.minutesAgo': '{minutes}分钟前',
                'time.hoursAgo': '{hours}小时前',
                
                // 错误信息
                'error.storageNotSupported': '您的浏览器不支持数据存储功能',
                'error.saveGameFailed': '保存游戏失败',
                'error.loadGameFailed': '加载游戏失败',
                'error.networkError': '网络错误',
                'error.invalidInput': '输入无效',
                'error.playerNameExists': '玩家名称已存在',
                'error.levelNotFound': '关卡未找到',
                'error.levelSaveFailed': '关卡保存失败'
            },
            
            'en-US': {
                // Game basic info
                'game.title': 'Split-Second Spark',
                'game.subtitle': 'Capture decisive moments, ignite infinite possibilities',
                'loading.text': 'Loading...',
                
                // Main menu
                'menu.start': 'Start Game',
                'menu.levelEditor': 'Level Editor',
                'menu.customLevels': 'Custom Levels',
                'menu.leaderboard': 'Leaderboard',
                'menu.settings': 'Settings',
                
                // Game interface
                'game.score': 'Score',
                'game.level': 'Level',
                'game.lives': 'Lives',
                'game.pause': 'Pause',
                'game.hint.ready': 'Ready?',
                'game.hint.start': 'Click to start!',
                'game.hint.perfect': 'Perfect timing!',
                'game.hint.good': 'Good!',
                'game.hint.miss': 'Missed!',
                'game.hint.gameOver': 'Game Over',
                
                // Pause menu
                'pause.title': 'Game Paused',
                'pause.resume': 'Resume',
                'pause.restart': 'Restart',
                'pause.mainMenu': 'Main Menu',
                
                // Game over
                'gameOver.title': 'Game Over',
                'gameOver.finalScore': 'Final Score',
                'gameOver.newRecord': 'New Record!',
                'gameOver.playAgain': 'Play Again',
                'gameOver.backToMenu': 'Back to Menu',
                
                // Settings
                'settings.title': 'Settings',
                'settings.language': 'Language',
                'settings.sound': 'Sound',
                'settings.music': 'Music',
                'settings.save': 'Save Settings',
                'settings.cancel': 'Cancel',
                
                // Player management
                'player.current': 'Current Player',
                'player.switch': 'Switch',
                'player.management': 'Player Management',
                'player.existing': 'Existing Players',
                'player.createNew': 'Create New Player',
                'player.namePlaceholder': 'Enter player name',
                'player.create': 'Create',
                'player.select': 'Select',
                'player.delete': 'Delete',
                'player.guest': 'Guest',
                
                // Leaderboard
                'leaderboard.title': 'Leaderboard',
                'leaderboard.global': 'Global',
                'leaderboard.daily': 'Daily',
                'leaderboard.weekly': 'Weekly',
                'leaderboard.monthly': 'Monthly',
                'leaderboard.perfectHits': 'Perfect Hits',
                'leaderboard.combo': 'Max Combo',
                'leaderboard.rank': 'Rank',
                'leaderboard.player': 'Player',
                'leaderboard.score': 'Score',
                'leaderboard.level': 'Level',
                'leaderboard.time': 'Time',
                'leaderboard.date': 'Date',
                'leaderboard.refresh': 'Refresh',
                'leaderboard.loading': 'Loading...',
                'leaderboard.empty': 'No leaderboard data',
                'leaderboard.beFirst': 'Be the first to make the leaderboard!',
                'leaderboard.error': 'Failed to load leaderboard',
                'leaderboard.yourRank': 'Your Rank',
                'leaderboard.outOf': '/',
                'leaderboard.yourScore': 'Your Score',
                'leaderboard.notRanked': 'Not ranked yet',
                
                // Level editor
                'editor.title': 'Level Editor',
                'editor.tools': 'Tools',
                'editor.select': 'Select',
                'editor.spark': 'Spark',
                'editor.obstacle': 'Obstacle',
                'editor.powerup': 'Powerup',
                'editor.trigger': 'Trigger',
                'editor.eraser': 'Eraser',
                'editor.actions': 'Actions',
                'editor.new': 'New',
                'editor.load': 'Load',
                'editor.save': 'Save',
                'editor.test': 'Test',
                'editor.publish': 'Publish',
                'editor.view': 'View',
                'editor.showGrid': 'Show Grid',
                'editor.snapToGrid': 'Snap to Grid',
                'editor.levelSettings': 'Level Settings',
                'editor.levelName': 'Level Name',
                'editor.levelDescription': 'Level Description',
                'editor.difficulty': 'Difficulty',
                'editor.difficulty.easy': 'Easy',
                'editor.difficulty.normal': 'Normal',
                'editor.difficulty.hard': 'Hard',
                'editor.difficulty.expert': 'Expert',
                'editor.timeLimit': 'Time Limit (seconds)',
                'editor.targetScore': 'Target Score',
                'editor.objectProperties': 'Object Properties',
                'editor.selectTool': 'Please select a tool',
                'editor.statistics': 'Statistics',
                'editor.totalObjects': 'Total Objects',
                'editor.sparkCount': 'Spark Count',
                'editor.obstacleCount': 'Obstacle Count',
                'editor.powerupCount': 'Powerup Count',
                'editor.newLevel': 'New Level',
                'editor.noObjectsToTest': 'No objects to test in level',
                'editor.testFailed': 'Failed to test level',
                'editor.validationFailed': 'Level validation failed',
                'editor.publishSuccess': 'Level published successfully!',
                'editor.publishFailed': 'Failed to publish level',
                'editor.noLevel': 'No level',
                'editor.nameRequired': 'Level name is required',
                'editor.noObjects': 'No objects in level',
                'editor.noSparks': 'No sparks in level',
                'editor.objectsOutOfBounds': 'Objects are out of bounds',
                'editor.selectSubType': 'Please select a subtype',
                'editor.loadLevel': 'Load Level',
                'editor.loadSelected': 'Load Selected',
                'editor.noLevels': 'No saved levels',
                'editor.noDescription': 'No description',
                'editor.selectLevel': 'Please select a level',
                'editor.loadFailed': 'Failed to load level',
                'editor.saveSuccess': 'Level saved successfully!',
                'editor.saveFailed': 'Failed to save level',
                'editor.sparkProperties': 'Spark Properties',
                'editor.obstacleProperties': 'Obstacle Properties',
                'editor.powerupProperties': 'Powerup Properties',
                'editor.spark.normal': 'Normal Spark',
                'editor.spark.fast': 'Fast Spark',
                'editor.spark.slow': 'Slow Spark',
                'editor.spark.bonus': 'Bonus Spark',
                'editor.spark.perfect': 'Perfect Spark',
                'editor.obstacle.wall': 'Wall',
                'editor.obstacle.moving_wall': 'Moving Wall',
                'editor.obstacle.spike': 'Spike',
                'editor.powerup.slow_time': 'Slow Time',
                'editor.powerup.double_score': 'Double Score',
                'editor.powerup.shield': 'Shield',
                'editor.powerup.multi_hit': 'Multi Hit',
                'editor.publish': 'Publish Level',
                'editor.name': 'Level Name',
                'editor.description': 'Level Description',
                'editor.difficulty': 'Difficulty',
                'editor.difficulty.easy': 'Easy',
                'editor.difficulty.normal': 'Normal',
                'editor.difficulty.hard': 'Hard',
                'editor.difficulty.expert': 'Expert',
                
                // Custom levels
                'customLevels.title': 'Custom Levels',
                'customLevels.subtitle': 'Explore amazing levels created by players',
                'customLevels.myLevels': 'My Levels',
                'customLevels.allLevels': 'All Levels',
                'customLevels.play': 'Play',
                'customLevels.edit': 'Edit',
                'customLevels.delete': 'Delete',
                'customLevels.like': 'Like',
                'customLevels.dislike': 'Dislike',
                'customLevels.liked': 'Liked',
                'customLevels.disliked': 'Disliked',
                'customLevels.rating': 'Rating',
                'customLevels.plays': 'Plays',
                'customLevels.playCount': 'Play Count',
                'customLevels.author': 'Author',
                'customLevels.difficulty': 'Difficulty',
                'customLevels.created': 'Created',
                'customLevels.description': 'Description',
                'customLevels.preview': 'Preview',
                'customLevels.details': 'Details',
                'customLevels.myLevel': 'Mine',
                'customLevels.empty': 'No custom levels yet',
                'customLevels.empty.title': 'No Levels',
                'customLevels.empty.message': 'No levels found matching the criteria',
                'customLevels.createFirst': 'Create First Level',
                'customLevels.loading': 'Loading levels...',
                'customLevels.totalLevels': 'Total Levels',
                'customLevels.noDescription': 'No description',
                'customLevels.emptyLevel': 'Empty level',
                'customLevels.filter': 'Filter',
                'customLevels.filter.published': 'Published',
                'customLevels.filter.my': 'My Levels',
                'customLevels.filter.all': 'All',
                'customLevels.sort': 'Sort',
                'customLevels.sort.rating': 'Rating',
                'customLevels.sort.playCount': 'Play Count',
                'customLevels.sort.date': 'Date',
                'customLevels.sort.difficulty': 'Difficulty',
                'customLevels.search': 'Search levels...',
                'customLevels.searchBtn': 'Search',
                'customLevels.loginToRate': 'Login to rate',
                'customLevels.noPermission': 'No permission',
                'customLevels.confirmDelete': 'Are you sure you want to delete level "{{name}}"?',
                'customLevels.deleteSuccess': 'Level deleted successfully',
                'customLevels.deleteFailed': 'Failed to delete level',
                'customLevels.loadFailed': 'Failed to load level',
                'customLevels.playFailed': 'Failed to play level',
                'customLevels.rateFailed': 'Failed to rate level',
                
                // Common
                'common.ok': 'OK',
                'common.cancel': 'Cancel',
                'common.close': 'Close',
                'common.back': 'Back',
                'common.next': 'Next',
                'common.previous': 'Previous',
                'common.loading': 'Loading...',
                'common.error': 'Error',
                'common.success': 'Success',
                'common.warning': 'Warning',
                'common.confirm': 'Confirm',

                // Time format
                'time.justNow': 'Just now',
                'time.minutesAgo': '{minutes} minutes ago',
                'time.hoursAgo': '{hours} hours ago',
                
                // Error messages
                'error.storageNotSupported': 'Your browser does not support data storage',
                'error.saveGameFailed': 'Failed to save game',
                'error.loadGameFailed': 'Failed to load game',
                'error.networkError': 'Network error',
                'error.invalidInput': 'Invalid input',
                'error.playerNameExists': 'Player name already exists',
                'error.levelNotFound': 'Level not found',
                'error.levelSaveFailed': 'Failed to save level'
            }
        };
    }

    /**
     * 获取翻译文本
     * @param {string} key - 翻译键
     * @param {object} params - 参数对象
     * @returns {string} 翻译后的文本
     */
    t(key, params = {}) {
        const translation = this.translations[this.currentLanguage]?.[key] || 
                          this.translations['zh-CN']?.[key] || 
                          key;
        
        // 替换参数
        return translation.replace(/\{\{(\w+)\}\}/g, (match, param) => {
            return params[param] || match;
        });
    }

    /**
     * 切换语言
     * @param {string} language - 语言代码
     */
    async setLanguage(language) {
        if (this.translations[language]) {
            this.currentLanguage = language;

            // 保存语言设置到存储
            try {
                if (window.storageService && window.storageService.initialized) {
                    await storageService.put('settings.language', language);
                }
            } catch (error) {
                console.warn('无法保存语言设置到存储:', error);
            }

            this.applyTranslations();
            console.log(`语言已切换到: ${language}`);

            // 触发语言变更事件
            this.triggerLanguageChangeEvent(language);
        } else {
            console.warn(`不支持的语言: ${language}`);
        }
    }

    /**
     * 触发语言变更事件
     * @param {string} language - 新语言代码
     */
    triggerLanguageChangeEvent(language) {
        try {
            const event = new CustomEvent('languageChanged', {
                detail: { language }
            });
            window.dispatchEvent(event);
        } catch (error) {
            console.warn('无法触发语言变更事件:', error);
        }
    }

    /**
     * 获取当前语言
     * @returns {string} 当前语言代码
     */
    getCurrentLanguage() {
        return this.currentLanguage;
    }

    /**
     * 应用翻译到页面元素
     */
    applyTranslations() {
        // 翻译所有带有 data-i18n 属性的元素
        const elements = document.querySelectorAll('[data-i18n]');
        elements.forEach(element => {
            const key = element.getAttribute('data-i18n');
            element.textContent = this.t(key);
        });

        // 翻译所有带有 data-i18n-placeholder 属性的元素
        const placeholderElements = document.querySelectorAll('[data-i18n-placeholder]');
        placeholderElements.forEach(element => {
            const key = element.getAttribute('data-i18n-placeholder');
            element.placeholder = this.t(key);
        });

        // 翻译所有带有 data-i18n-title 属性的元素
        const titleElements = document.querySelectorAll('[data-i18n-title]');
        titleElements.forEach(element => {
            const key = element.getAttribute('data-i18n-title');
            element.title = this.t(key);
        });

        // 更新页面标题
        document.title = this.t('game.title') + ' - Split-Second Spark';
    }

    /**
     * 获取支持的语言列表
     * @returns {Array} 语言列表
     */
    getSupportedLanguages() {
        return [
            { code: 'zh-CN', name: '中文' },
            { code: 'en-US', name: 'English' }
        ];
    }

    /**
     * 检测浏览器语言
     * @returns {string} 检测到的语言代码
     */
    detectBrowserLanguage() {
        const browserLang = navigator.language || navigator.userLanguage;
        
        // 检查是否支持检测到的语言
        if (this.translations[browserLang]) {
            return browserLang;
        }
        
        // 检查语言的主要部分（如 en-GB -> en）
        const mainLang = browserLang.split('-')[0];
        const supportedLangs = Object.keys(this.translations);
        const matchedLang = supportedLangs.find(lang => lang.startsWith(mainLang));
        
        return matchedLang || 'zh-CN'; // 默认返回中文
    }
}

// 创建全局国际化服务实例
window.i18nService = new I18nService();
