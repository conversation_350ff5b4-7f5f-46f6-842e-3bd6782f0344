/**
 * 瞬光捕手 - 主程序入口
 * 负责初始化所有系统模块并启动游戏
 */

class GameApplication {
    constructor() {
        this.initialized = false;
        this.modules = [];
        this.startTime = Date.now();
    }

    /**
     * 初始化游戏应用
     */
    async init() {
        try {
            console.log('🎮 瞬光捕手 - 游戏启动中...');
            
            // 显示加载界面
            this.showLoadingScreen();
            
            // 按顺序初始化各个模块
            await this.initializeModules();
            
            // 设置全局错误处理
            this.setupErrorHandling();
            
            // 设置性能监控
            this.setupPerformanceMonitoring();
            
            // 启动游戏循环
            this.startGameLoop();
            
            this.initialized = true;
            const loadTime = Date.now() - this.startTime;
            console.log(`✅ 游戏初始化完成，耗时: ${loadTime}ms`);
            
        } catch (error) {
            console.error('❌ 游戏初始化失败:', error);
            this.showErrorScreen(error);
        }
    }

    /**
     * 显示加载界面
     */
    showLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            loadingScreen.classList.remove('hidden');
            loadingScreen.classList.add('active');
        }
    }

    /**
     * 按顺序初始化各个模块
     */
    async initializeModules() {
        // 首先尝试初始化 EdgeOne 云存储（如果可用）
        await this.initEdgeOneStorageIfAvailable();

        const initSteps = [
            { name: '存储服务', module: 'storageService', init: () => this.initModule('storageService', window.storageService) },
            { name: '国际化服务', module: 'i18nService', init: () => this.initModule('i18nService', window.i18nService) },
            { name: '玩家管理器', module: 'playerManager', init: () => this.initModule('playerManager', window.playerManager) },
            { name: '关卡管理器', module: 'levelManager', init: () => this.initModule('levelManager', window.levelManager) },
            { name: '排行榜管理器', module: 'leaderboardManager', init: () => this.initModule('leaderboardManager', window.leaderboardManager) },
            { name: '游戏引擎', module: 'gameEngine', init: () => this.initModule('gameEngine', window.gameEngine) },
            { name: '关卡编辑器', module: 'levelEditor', init: () => this.initModule('levelEditor', window.levelEditor) },
            { name: '屏幕管理器', module: 'screenManager', init: () => this.initModule('screenManager', window.screenManager) },
            { name: '输入处理器', module: 'inputHandler', init: () => this.initModule('inputHandler', window.inputHandler) }
        ];

        for (let i = 0; i < initSteps.length; i++) {
            const step = initSteps[i];

            try {
                console.log(`📦 正在初始化: ${step.name}...`);
                this.updateLoadingProgress(step.name, (i / initSteps.length) * 100);

                await step.init();
                this.modules.push(step.module);

                console.log(`✅ ${step.name} 初始化完成`);

            } catch (error) {
                console.error(`❌ ${step.name} 初始化失败:`, error);
                throw new Error(`${step.name}初始化失败: ${error.message}`);
            }
        }

        this.updateLoadingProgress('初始化完成', 100);
    }

    /**
     * 初始化 EdgeOne 云存储（如果可用）
     * 这个方法会在存储服务初始化之前调用，确保云存储能被正确检测
     */
    async initEdgeOneStorageIfAvailable() {
        try {
            // 检查是否有 EdgeOne 存储初始化器
            if (typeof window.initEdgeOneStorage === 'function') {
                console.log('🌐 检测到 EdgeOne 存储初始化器，尝试初始化云存储...');

                // 获取 EdgeOne 存储配置
                const edgeOneConfig = this.getEdgeOneStorageConfig();

                // 初始化 EdgeOne 存储
                const success = await window.initEdgeOneStorage(edgeOneConfig);

                if (success) {
                    console.log('✅ EdgeOne 云存储初始化成功');

                    // 显示存储状态信息
                    if (typeof window.getEdgeOneStorageStatus === 'function') {
                        const status = window.getEdgeOneStorageStatus();
                        console.log('📊 EdgeOne 存储状态:', status);
                    }
                } else {
                    console.log('⚠️ EdgeOne 云存储初始化失败，将使用本地存储');
                }

                return success;
            } else {
                console.log('📝 EdgeOne 存储初始化器未找到，跳过云存储初始化');
                return false;
            }
        } catch (error) {
            console.warn('⚠️ EdgeOne 云存储初始化过程中出现错误:', error);
            return false;
        }
    }

    /**
     * 获取 EdgeOne 存储配置
     * 可以根据游戏的具体需求来定制配置
     * @returns {Object} EdgeOne 存储配置
     */
    getEdgeOneStorageConfig() {
        // 基础配置
        const config = {
            // 生产环境 API URL（如果需要）
            // productionApiUrl: 'https://your-domain.com',

            // 自定义配置覆盖
            customConfig: {
                // 启用连接测试
                testConnection: true,

                // 根据游戏需求调整超时时间
                timeout: 12000,

                // 启用缓存以提高性能
                enableCache: true,

                // 启用降级机制确保游戏可用性
                enableFallback: true,

                // 重试配置
                retryCount: 2,
                retryDelay: 1000
            }
        };

        // 可以根据当前环境或用户设置进一步定制配置
        // 例如：检查用户是否启用了云存储功能

        console.log('🔧 EdgeOne 存储配置:', config);
        return config;
    }

    /**
     * 安全初始化单个模块
     */
    async initModule(moduleName, moduleInstance) {
        // 检查模块是否存在
        if (!moduleInstance) {
            console.error(`❌ ${moduleName} 模块未定义，检查脚本加载顺序`);

            // 尝试从全局作用域获取模块
            const globalModule = window[moduleName];
            if (globalModule) {
                console.log(`✅ 从全局作用域找到 ${moduleName}`);
                moduleInstance = globalModule;
            } else {
                throw new Error(`${moduleName} 模块未定义且无法从全局作用域获取`);
            }
        }

        // 检查模块是否有init方法
        if (typeof moduleInstance.init !== 'function') {
            console.warn(`⚠️ ${moduleName} 没有init方法，跳过初始化`);
            return;
        }

        // 调用初始化方法
        console.log(`🔧 正在初始化 ${moduleName}...`);
        await moduleInstance.init();

        // 验证初始化是否成功
        if (moduleInstance.initialized !== true) {
            console.warn(`⚠️ ${moduleName} 初始化后状态异常`);
        } else {
            console.log(`✅ ${moduleName} 初始化成功`);
        }
    }

    /**
     * 更新加载进度
     */
    updateLoadingProgress(message, progress) {
        const loadingText = document.querySelector('#loading-screen p');
        if (loadingText) {
            loadingText.textContent = `${message}... ${Math.round(progress)}%`;
        }
    }

    /**
     * 设置全局错误处理
     */
    setupErrorHandling() {
        // 捕获未处理的错误
        window.addEventListener('error', (event) => {
            console.error('全局错误:', event.error);
            this.handleError(event.error);
        });

        // 捕获未处理的Promise拒绝
        window.addEventListener('unhandledrejection', (event) => {
            console.error('未处理的Promise拒绝:', event.reason);
            this.handleError(event.reason);
        });
    }

    /**
     * 处理错误
     */
    handleError(error) {
        // 记录错误到存储
        this.logError(error);
        
        // 根据错误类型决定处理方式
        if (this.isCriticalError(error)) {
            this.showErrorScreen(error);
        } else {
            this.showErrorNotification(error);
        }
    }

    /**
     * 记录错误日志
     */
    async logError(error) {
        try {
            const errorLog = {
                timestamp: Date.now(),
                message: error.message || error.toString(),
                stack: error.stack,
                userAgent: navigator.userAgent,
                url: window.location.href,
                player: playerManager.getCurrentPlayer()?.name || 'unknown'
            };
            
            await storageService.put(`error.${Date.now()}`, errorLog);
        } catch (logError) {
            console.error('记录错误日志失败:', logError);
        }
    }

    /**
     * 判断是否为严重错误
     */
    isCriticalError(error) {
        const criticalKeywords = ['init', 'storage', 'canvas', 'webgl'];
        const errorMessage = (error.message || error.toString()).toLowerCase();
        
        return criticalKeywords.some(keyword => errorMessage.includes(keyword));
    }

    /**
     * 显示错误界面
     */
    showErrorScreen(error) {
        const errorHtml = `
            <div class="error-screen">
                <div class="error-content">
                    <h2>😔 游戏遇到了问题</h2>
                    <p>很抱歉，游戏无法正常运行。</p>
                    <details>
                        <summary>错误详情</summary>
                        <pre>${error.message || error.toString()}</pre>
                    </details>
                    <div class="error-actions">
                        <button onclick="location.reload()">重新加载</button>
                        <button onclick="localStorage.clear(); location.reload()">清除数据并重新加载</button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.innerHTML = errorHtml;
    }

    /**
     * 显示错误通知
     */
    showErrorNotification(error) {
        // 创建错误通知元素
        const notification = document.createElement('div');
        notification.className = 'error-notification';
        notification.innerHTML = `
            <div class="notification-content">
                <span class="notification-icon">⚠️</span>
                <span class="notification-message">${error.message || '发生了一个错误'}</span>
                <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `;
        
        // 添加样式
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #f44336;
            color: white;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 10000;
            max-width: 300px;
            animation: slideIn 0.3s ease-out;
        `;
        
        document.body.appendChild(notification);
        
        // 自动移除通知
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }

    /**
     * 设置性能监控
     */
    setupPerformanceMonitoring() {
        // 监控帧率
        let frameCount = 0;
        let lastTime = performance.now();
        
        const monitorFPS = () => {
            frameCount++;
            const currentTime = performance.now();
            
            if (currentTime - lastTime >= 1000) {
                const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
                
                // 如果帧率过低，显示警告
                if (fps < 30 && gameEngine.gameState === 'playing') {
                    console.warn(`⚠️ 帧率较低: ${fps} FPS`);
                }
                
                frameCount = 0;
                lastTime = currentTime;
            }
            
            requestAnimationFrame(monitorFPS);
        };
        
        requestAnimationFrame(monitorFPS);
        
        // 监控内存使用（如果支持）
        if ('memory' in performance) {
            setInterval(() => {
                const memory = performance.memory;
                const usedMB = Math.round(memory.usedJSHeapSize / 1024 / 1024);
                const totalMB = Math.round(memory.totalJSHeapSize / 1024 / 1024);
                
                if (usedMB > 100) { // 超过100MB时警告
                    console.warn(`⚠️ 内存使用较高: ${usedMB}MB / ${totalMB}MB`);
                }
            }, 30000); // 每30秒检查一次
        }
    }

    /**
     * 启动游戏循环
     */
    startGameLoop() {
        let lastTime = 0;
        
        const gameLoop = (currentTime) => {
            const deltaTime = currentTime - lastTime;
            lastTime = currentTime;
            
            // 更新输入处理器（手柄支持）
            if (inputHandler.initialized) {
                inputHandler.updateGamepad();
            }
            
            // 继续循环
            requestAnimationFrame(gameLoop);
        };
        
        requestAnimationFrame(gameLoop);
    }

    /**
     * 获取系统信息
     */
    getSystemInfo() {
        return {
            userAgent: navigator.userAgent,
            language: navigator.language,
            platform: navigator.platform,
            cookieEnabled: navigator.cookieEnabled,
            onLine: navigator.onLine,
            screen: {
                width: screen.width,
                height: screen.height,
                colorDepth: screen.colorDepth
            },
            viewport: {
                width: window.innerWidth,
                height: window.innerHeight
            },
            storage: {
                localStorage: typeof Storage !== 'undefined',
                indexedDB: 'indexedDB' in window
            },
            webgl: this.checkWebGLSupport(),
            touchSupport: 'ontouchstart' in window,
            gamepadSupport: 'getGamepads' in navigator
        };
    }

    /**
     * 检查WebGL支持
     */
    checkWebGLSupport() {
        try {
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            return !!gl;
        } catch (e) {
            return false;
        }
    }

    /**
     * 清理资源
     */
    cleanup() {
        console.log('🧹 清理游戏资源...');
        
        // 清理各个模块
        if (inputHandler && inputHandler.cleanup) {
            inputHandler.cleanup();
        }
        
        if (gameEngine && gameEngine.animationId) {
            cancelAnimationFrame(gameEngine.animationId);
        }
        
        console.log('✅ 资源清理完成');
    }
}

// 页面加载完成后启动游戏
document.addEventListener('DOMContentLoaded', async () => {
    console.log('📄 DOM加载完成，启动游戏应用...');
    
    // 创建游戏应用实例
    window.gameApp = new GameApplication();
    
    // 启动游戏
    await gameApp.init();
    
    // 页面卸载时清理资源
    window.addEventListener('beforeunload', () => {
        if (gameApp) {
            gameApp.cleanup();
        }
    });
    
    // 输出系统信息（调试用）
    console.log('🔧 系统信息:', gameApp.getSystemInfo());
});

// 添加一些CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    .error-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-family: 'Microsoft YaHei', Arial, sans-serif;
        z-index: 10000;
    }
    
    .error-content {
        text-align: center;
        max-width: 500px;
        padding: 2rem;
        background: rgba(0, 0, 0, 0.3);
        border-radius: 15px;
        backdrop-filter: blur(10px);
    }
    
    .error-content h2 {
        margin-bottom: 1rem;
        font-size: 2rem;
    }
    
    .error-content details {
        margin: 1rem 0;
        text-align: left;
    }
    
    .error-content pre {
        background: rgba(0, 0, 0, 0.5);
        padding: 1rem;
        border-radius: 5px;
        overflow-x: auto;
        font-size: 0.8rem;
    }
    
    .error-actions {
        margin-top: 2rem;
    }
    
    .error-actions button {
        margin: 0.5rem;
        padding: 10px 20px;
        background: #f44336;
        color: white;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-size: 1rem;
    }
    
    .error-actions button:hover {
        background: #d32f2f;
    }
    
    .notification-content {
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .notification-close {
        background: none;
        border: none;
        color: white;
        font-size: 1.2rem;
        cursor: pointer;
        padding: 0;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
`;
document.head.appendChild(style);
